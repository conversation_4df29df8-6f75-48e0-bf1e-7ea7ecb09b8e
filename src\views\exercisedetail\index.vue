<script setup lang="ts">
import ExerInfoBlock from "@/components/ExerInfoBlock.vue"
import questionListTableForExer from "@/views/prjdetail/components/questionListTableForExer.vue"
import modeTypeSwitcher from "@/views/prjdetail/components/modeTypeSwitcher.vue"
import QuestionDrawer from "@/components/QuestionDrawer.vue"
import AnswerDrawer from "@/components/AnswerDrawer.vue"
import MulQuestionList from "../prjdetail/components/MulQuestionList.vue"

import router from "@/router"
import { onMounted, onUnmounted, provide, ref, triggerRef, watch } from "vue"
import { getExerDetailApi } from "@/apis/path/exercise"
import { getQuestionList } from "@/utils/lineWord2V2"
import { ElMessage } from "element-plus"
import { useRoute } from "vue-router"
import { useRouterPushStore } from "@/stores/routerPushStore"
import { emitter } from "@/utils/emitter"
import { Event } from "@/types/event"
import { Exercise } from "@/types/exercise"
import { processAllLatexEquations } from "@/utils/latexUtils"
import { renderMarkdown } from "@/utils/markdown";
import QuestionDialog from '@/components/QuestionDialog.vue'
import AnswerDialog from "@/components/AnswerDialog.vue"

const routerPushStore = useRouterPushStore()
const curHeaderMode = ref(0)
const questionDrawerRef = ref()
const answerDrawerRef = ref()
const mulQuesionListRef = ref()
const toOpenQuesId = ref()
const exerciseId = ref()
const route = useRoute()
provide("toOpenQuestion", toOpenQuesId)

const questionOnwer = ref({
  exerciseId: "",
  onwerType: 0,
})
const exercise = ref<Exercise>({
  type: 0,
  stem: "",
  content: [],
  answer: "",
  explanation: ""
})

// 处理转换header的mode
const handleChangeHeader = (newHeaderMode: number) => {
  curHeaderMode.value = newHeaderMode
  sessionStorage.setItem('mode', `${curHeaderMode.value}`)
}
// 获取习题详情
const getExerciseDetail = async () => {
  exercise.value = await routerPushStore.getData()
  console.log("exercise.value", exercise.value)
}
const handleClickWord = (el: HTMLElement) => {
  console.log("el", el)
  // 如果元素为空或没有data-qid属性，直接返回
  if (!el) return
  const id = (el as HTMLElement).getAttribute("data-qid")
  if (!id) {
    console.log("元素没有data-qid属性")
    return
  }

  const idList = id.split(",")
  if (idList.length > 1) {
    getQuestionList(id).then((questionList) => {
      const query = {
        questionList: questionList,
        element: el,
      }
      emitter.emit(Event.SHOW_MUL_LIST, query)
    })
  } else if (idList.length === 1) {
    emitter.emit(Event.SHOW_ANSWER_DRAWER, { mode: 3, item: parseInt(id) })
  }
}
watch(
  () => route.query.uniqueCode,
  () => {
    if (route.query.uniqueCode) {
      exerciseId.value = route.query.uniqueCode.toString()
      questionOnwer.value.exerciseId = exerciseId.value
      getExerciseDetail()
    }
  },
  { deep: true, immediate: true }
)
onMounted(() => {
  emitter.on(Event.CLICK_WORD, handleClickWord)
  if (route.query.questionId) {
    toOpenQuesId.value = route.query.questionId
  }
  curHeaderMode.value = Number(sessionStorage.getItem('mode'))
})
onUnmounted(() => {
  emitter.off(Event.CLICK_WORD, handleClickWord)
  sessionStorage.removeItem('mode')
  routerPushStore.setData(null)
})
const mark =
  `$$\begin{align}(a+b)^2 &= a^2 + 2ab + b^2 \\(a+b)(a-b) &= a^2 - b^2\end{align}$$
`
</script>
<template>
  <div class="wrapper">
    <!-- <div v-html="processAllLatexEquations(mark)"></div> -->
    <div class="header">
      <mode-type-switcher :mode="curHeaderMode" @changeMode="handleChangeHeader">
        <template v-slot:mode0> 习题内容 </template>
        <template v-slot:mode1> 问题列表 </template>
      </mode-type-switcher>
      <div class="btn-container">
        <CmpButton type="primary" class="w130">生成问题</CmpButton>
      </div>
    </div>
    <!--<div class="line"></div>-->
    <div class="info-container" v-show="curHeaderMode === 0">
      <ExerInfoBlock :exercise="exercise"></ExerInfoBlock>
    </div>
    <span class="table-container" v-show="curHeaderMode === 1">
      <questionListTableForExer :exercise-id="exerciseId"></questionListTableForExer>
    </span>
  </div>
  <!-- 其他组件 -->
  <!-- 问题抽屉 -->
  <question-drawer ref="questionDrawerRef" :ques-onwer="questionOnwer"></question-drawer>
  <answer-drawer ref="answerDrawerRef" :ques-onwer="questionOnwer"></answer-drawer>
  <!-- 划词浮窗 -->
  <mul-question-list ref="mulQuesionListRef"></mul-question-list>
  <QuestionDialog></QuestionDialog>
  <AnswerDialog></AnswerDialog>
</template>
<style scoped>
.line {
  width: 1240px;
  height: 1px;
  background-color: #ebeef5;
  margin: 0 auto;
}

.wrapper {
  background-color: white;
  width: var(--width-fixed--project);
  padding: 10px 0;
  margin: 0 auto 0;
  display: flex;
  flex-direction: column;

  .header {
    width: 1240px;
    display: flex;
    justify-content: center;
    position: relative;
    margin: 0 auto;
    margin-bottom: 25px;
  }

  .btn-container {
    position: absolute;
    right: 0;
    top: 10px;

    .w130 {
      width: 120px;
      height: 35px;
      font-size: 14px;
      border-radius: 4px;
    }

  }

  .info-container {
    width: 1240px;
    margin: 0 auto;
    display: flex;
    height: calc(100% - 60px);
    /* 减去header和line的高度 */
    overflow: hidden;
  }

  .table-container {
    display: flex;
    width: 100%;
    justify-content: center;
    padding: 10px 20px;
  }
}
</style>
