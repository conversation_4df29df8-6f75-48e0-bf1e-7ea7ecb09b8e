<script setup lang="ts">
// 划词后的提问页面
import classicEditor from '@/components/editors/Veditor.vue';
import MyButton from '@/components/MyButton.vue';

import { ElFormItem, ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { qMode, qModeDict, qType, qTypeDict, qWeight, qWeightDict } from '@/utils/constant';

import type { questionItem, klgType } from '@/utils/type';
import { editQuestionApi, params2EditQues } from '@/apis/path/prjdetail';
import { userInfoStore } from '@/stores/userInfo';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { markWord4ExerciseApi, params2MarkWord } from '@/apis/path/exercise';
import { markWords4Text, param4mark } from '@/apis/path/lineWordApis';
import { useElementStore } from '@/stores/drawerElement';
import { findKeyByValue } from '@/utils/func';
import { renderMarkdown } from '@/utils/markdown';
import { useDrawerControllerStore } from '@/stores/drawerController';
const elStore = useElementStore();
const userinfo = userInfoStore();
const drawerControllerStore = useDrawerControllerStore();
const quesType = ['是什么', '为什么', '怎么做', '开放性问题'];
const quesNecess = ['必要问题', '参考问题'];
const quesPersonal = ['公开问题', '个人问题'];
const isDrawerShow = ref(false); // 是否展示抽屉
const drawerTitle = ref(); // 抽屉名
const questionDescriptionRef = ref(); // 开放性问题描述editor
const ruleAddFormRef = ref<FormInstance>();
const curMode = ref(-1); // 0新增|1编辑|2只读
const answerList = ref([]);
const isAddTaskShow = ref<boolean>(true);
const isAddTagShow = ref<boolean>(true);
const curAnswerKlg = ref<klgType[]>([]);
const questionLocation = ref<any>();

const props = defineProps({
  quesOnwer: Object
});

// 计算未删除的任务数量
const tasksLength = computed(() => {
  return ruleForm.list.filter((task) => !task.deleted).length;
});

const curQuestion = reactive<questionItem>({
  questionId: -1,
  questionDescription: '',
  associatedWords: '',
  keyword: '',
  questionType: '是什么',
  questionNecessity: qMode.ness,
  questionWeight: qWeight.open,
  userName: userinfo.getUsername(),
  createTime: '',
  answerNumber: 0,
  questionState: '',
  canDelete: true
});

const ruleForm = reactive({
  answer: '',
  list: []
});

const rule4ques = reactive<FormRules>({
  keyword: [{ required: true, message: '请输入关键字', trigger: 'blur' }],
  questionDescription: [{ required: true, message: '请输入开放问题描述', trigger: 'blur' }]
});

// 展示抽屉
const showDrawer = (query: any) => {
  const { mode, item } = query;
  curMode.value = mode; // 2:不可操作
  switch (curMode.value) {
    case 0:
      drawerTitle.value = '提问';
      questionLocation.value = item;

      // 当处于提问模式时，确保内容已经被convertMathTagsToMDLatex处理过

      let ques: questionItem = {
        questionId: -1,
        questionDescription: '',
        associatedWords: item.content,
        keyword: item.content,
        questionType: findKeyByValue(qType.what, qTypeDict) as string,
        questionNecessity: qMode.ness,
        questionWeight: qWeight.open,
        userName: userinfo.getUsername(),
        createTime: '',
        answerNumber: 0,
        questionState: '',
        canDelete: true
      };
      Object.assign(curQuestion, ques);
      curQuestion.userName = userinfo.getUsername();
      break;
    case 1:
      drawerTitle.value = '编辑问题';
      Object.assign(curQuestion, item);
      break;
    default:
      console.log('问题弹窗mode异常');
      break;
  }
  isDrawerShow.value = true;
};

// 处理关闭抽屉
const handleClose = () => {
  window.getSelection()?.empty();
  curAnswerKlg.value.splice(0, curAnswerKlg.value.length);
  ruleForm.list.splice(0, ruleForm.list.length);
  answerList.value.splice(0, answerList.value.length);
  ruleForm.answer = '';
  isDrawerShow.value = false;
  elStore.setNull();
  // 清空lineWordContent，确保isQuestionDrawerOpen状态被重置
  emitter.emit('clear_line_word_content');
};

// 处理提交添加问题
const handleAddSubmit = () => {
  enum OnwerType {
    exercise = 0,
    prj = 1
  }
  ruleAddFormRef.value.validate((valid) => {
    if (valid) {
      if (props.quesOnwer.onwerType === OnwerType.exercise) {
        const params2addQ = ref<params2MarkWord>({
          exerciseId: props.quesOnwer.exerciseId,
          keyword: curQuestion.keyword, // 纯文本
          questionType: qTypeDict[curQuestion.questionType],
          questionNecessity: curQuestion.questionNecessity,
          associatedWords: curQuestion.associatedWords, // 带span标签
          questionWeight: curQuestion.questionWeight,
          questionDescription: curQuestion.questionDescription
        });
        console.log('params2addQ.value', params2addQ.value);
        markWord4ExerciseApi(params2addQ.value).then((res) => {
          console.log('res', res);
          if (res.success) {
            ElMessage.success('添加成功！');
            emitter.emit(Event.ADD_QUESTION, res.data);
            emitter.emit(Event.REFRESH_QUESTION_LIST, true);
            drawerControllerStore.setMode(false); // 切换到阅读模式
            handleClose();
          } else {
            ElMessage.error(res.message);
          }
        });
      } else if (props.quesOnwer.onwerType === OnwerType.prj) {
        const params2addQ = ref<param4mark>({
          uniqueCode: props.quesOnwer.uniqueCode,
          chapterId: questionLocation.value.chapterId,
          contentId: questionLocation.value.contentId,
          keyword: curQuestion.keyword, // 纯文本
          questionType: qTypeDict[curQuestion.questionType],
          questionNecessity: curQuestion.questionNecessity,
          associatedWords: curQuestion.associatedWords, // 带span标签
          questionWeight: curQuestion.questionWeight,
          questionDescription: curQuestion.questionDescription
        });
        markWords4Text(params2addQ.value).then((res) => {
          if (res.success) {
            ElMessage.success('添加成功！');
            emitter.emit(Event.ADD_QUESTION, res.data);
            emitter.emit(Event.REFRESH_QUESTION_LIST, true);
            drawerControllerStore.setMode(false); // 切换到阅读模式
            handleClose();
          } else {
            ElMessage.error(res.message);
          }
        });
      }
    }
  });
};

// 处理提交编辑问题
const handleEditSubmit = () => {
  ruleAddFormRef.value.validate((valid) => {
    if (valid) {
      const params = ref<params2EditQues>({
        qid: curQuestion.questionId,
        keyword: curQuestion.keyword,
        associatedWords: curQuestion.associatedWords,
        questionType: curQuestion.questionType,
        questionNecessity: curQuestion.questionNecessity,
        questionWeight: curQuestion.questionWeight,
        questionDescription: curQuestion.questionDescription
      });
      editQuestionApi(params.value).then((res) => {
        if (res.success) {
          emitter.emit(Event.REFRESH_QUESTION_LIST, true);
          drawerControllerStore.setMode(false); // 切换到阅读模式
          handleClose();
          ElMessage.success('编辑问题成功');
        } else {
          ElMessage.error(res.message);
        }
      });
    }
  });
};

// 处理选择QType
const handleSelectQType = (type: string) => {
  if (curQuestion.userName !== userinfo.getUsername()) {
    ElMessage.warning('只能修改自己问题的问题类型');
    return;
  }
  if (curQuestion.answerNumber !== 0) {
    ElMessage.warning('已有答案，无法更改问题类型');
  } else {
    if (type === curQuestion.questionType) {
      return;
    } else {
      if (qTypeDict[curQuestion.questionType] === qType.open) {
        curQuestion.questionDescription = '';
      }
      curQuestion.questionType = type;
    }
  }
};
// 处理选择QNecess
const handleSelectQNecess = (qNecess: string) => {
  if (qModeDict[qNecess] === curQuestion.questionNecessity) {
    return;
  } else {
    curQuestion.questionNecessity = qModeDict[qNecess];
  }
};

// 处理选择QWeight
const handleSelectQWeight = (qWeight: string) => {
  if (qModeDict[qWeight] === curQuestion.questionWeight) {
    return;
  } else {
    curQuestion.questionWeight = qWeightDict[qWeight];
  }
};

watch(
  () => tasksLength.value,
  (newValue) => {
    if (qTypeDict[curQuestion.questionType] === qType.what) {
      if (newValue === 1) {
        isAddTagShow.value = false;
      } else if (newValue === 0) {
        isAddTagShow.value = true;
      }
    }
  }
);
watch(
  () => curAnswerKlg.value.length,
  (newValue) => {
    if (qTypeDict[curQuestion.questionType] === qType.what) {
      if (newValue === 1) {
        isAddTaskShow.value = false;
      } else if (newValue === 0) {
        isAddTaskShow.value = true;
      }
    }
  }
);
onMounted(() => {
  emitter.off(Event.SHOW_QUESTION_DRAWER, showDrawer);
  emitter.on(Event.SHOW_QUESTION_DRAWER, showDrawer);
});
onUnmounted(() => {
  emitter.off(Event.SHOW_QUESTION_DRAWER, showDrawer);
});
defineExpose({
  showDrawer,
  isDrawerShow
});
// 获取文档中所有的 <code> 标签
const codeElements = document.querySelectorAll('code');

// 遍历所有 <code> 标签并添加 hljs 类
codeElements.forEach((codeElement) => {
  codeElement.classList.add('hljs');
});
</script>

<template>
  <el-drawer direction="ltr" class="question-drawer" size="600" v-model="isDrawerShow"
    :show-close="false" :close-on-click-modal="false" :z-index="10" style="color: var(--color-black)">
    <template #header="{ titleId }">
      <div :id="titleId" class="title">
        {{ drawerTitle }}
      </div>
      <button class="el-drawer__close-btn btn-position" type="button" @click="handleClose"><i
          class="el-icon el-drawer__close"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
            <path fill="currentColor"
              d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z">
            </path>
          </svg></i></button>
    </template>
    <div class="line-title"></div>

    <!-- 编辑/添加问题表单 -->
    <template v-if="curMode === 1 || curMode === 0">
      <el-form ref="ruleAddFormRef" :model="curQuestion" :rules="rule4ques">
        <div class="content-container">
          <div class="related-content-container">
            <b class="ck-content questionList" style="overflow: hidden"
              v-html="renderMarkdown(curQuestion.associatedWords)"></b>
          </div>
          <div class="tip">
            <span style="font-size: 12px; font-weight: 700;"><b>{{ curQuestion.userName }}</b></span>
            <span style="margin-left: 10px; font-size: 12px;">的提问</span>
            <div style="margin-top: 15px;" v-if="curMode === 1 && qTypeDict[curQuestion.questionType] !== qType.open">
              <span>【{{ curQuestion.keyword }}】 </span><span>{{ curQuestion.questionType }}?</span>
            </div>
          </div>
          <el-form-item prop="questionDescription" v-if="qTypeDict[curQuestion.questionType] === qType.open">
            <div style="width: 100%;">
              <classicEditor v-if="isDrawerShow && curQuestion.userName === userinfo.getUsername()"
                ref="questionDescriptionRef" v-model="curQuestion.questionDescription" class="input"></classicEditor>
            </div>
          </el-form-item>
          <el-form-item prop="keyword" v-else>
            <span style="display: flex; word-break: break-all; width: 100%">
              <div style="width: 100%;">
                <classicEditor v-if="isDrawerShow && curQuestion.userName === userinfo.getUsername()"
                  v-model="curQuestion.keyword" :disabled="curQuestion.userName !== userinfo.getUsername()" class="input">
                </classicEditor>
              </div>
              <!--<span class="q-type" style="padding-top: 50px">
                <span>{{ curQuestion.questionType }}?</span>
              </span>-->
            </span>
          </el-form-item>
          <div class="type-container" v-if="curMode === 0">
            <span class="button-list">
              <span v-for="index in quesType" :key="index" style="width: 100%">
                <my-button @click="handleSelectQType(index)" style="margin-right: 15px; width: 100%" class="tag"
                  :class="curQuestion.questionType === index ? 'select-tag' : 'not-select-tag'" :type="'select-group'">
                  {{ index }}
                </my-button>
              </span>
            </span>
          </div>
          <el-form-item v-if="isDrawerShow && curQuestion.userName === userinfo.getUsername() && curMode === 1">
            <div class="type-container">
              <span class="button-list">
                <span v-for="index in quesType" :key="index" style="width: 100%">
                  <my-button :clickable="curQuestion.answerNumber === 0" @click="handleSelectQType(index)"
                    style="margin-right: 15px; width: 100%" class="tag"
                    :class="curQuestion.questionType === index ? 'select-tag' : 'not-select-tag'" :type="'select-group'">
                    {{ index }}
                  </my-button>
                </span>
              </span>
            </div>
          </el-form-item>
          <el-form-item>
            <div class="button-list">
              <div v-for="index in quesNecess" :key="index" style="width: 100%">
                <my-button @click="handleSelectQNecess(index)" style="width: 100%" class="tag" :class="curQuestion.questionNecessity === qModeDict[index]
                  ? 'select-tag'
                  : 'not-select-tag'
                  " :type="'select-group'">
                  {{ index }}
                </my-button>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <div class="button-list">
              <div v-for="index in quesPersonal" :key="index" style="width: 100%">
                <my-button @click="handleSelectQWeight(index)" style="width: 100%" class="tag" :class="curQuestion.questionWeight === qWeightDict[index]
                  ? 'select-tag'
                  : 'not-select-tag'
                  " :type="'select-group'">
                  {{ index }}
                </my-button>
              </div>
            </div>
          </el-form-item>
          <!--<div style="height: 1px; background-color: var(--color-boxborder)"></div>-->
        </div>
      </el-form>
    </template>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <template v-if="curMode === 1">
            <my-button type="light" @click="handleClose">关闭</my-button>
            <my-button @click="handleEditSubmit">提交</my-button>
          </template>
          <template v-else-if="curMode === 0">
            <my-button type="light" @click="handleClose">关闭</my-button>
            <my-button @click="handleAddSubmit">添加</my-button>
          </template>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
/* 列表标签不显示前缀样式
:deep(li::marker) {
  content: "";
  color: transparent;
}
*/
:deep(.el-form-item__error) {
  top: 95%;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__content) {
  align-items: center;
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  font-size: var(--font-size);
  line-height: 19px;
  min-width: 0;
  position: relative;
}

:deep(.el-select__caret.is-reverse) {
  transform: rotate(0deg);
}

.highlight {
  background-color: var(--color-light);
  color: var(--color-primary);
}

.question-drawer {
  color: var(--color-black);
}

.el-select-dropdown__item {
  color: var(--color-black);
  font-family: var(--font-family-text);

  &:hover * {
    font-weight: 600;
  }

  &.selected {
    color: var(--color-primary);
  }
}

.not-select-tag {
  background-color: white;
  color: black;
  border: 1px solid #dcdfe6;
}

.select-tag {
  background-color: #dcdfe6;
  color: black;
}

.title {
  font-size: 16px;
  font-weight: 700;
  font-family: var(--font-family-text);
  color: #333333;
  padding-bottom: 10px;
}

.btn-position {
  position: absolute;
  top: 10px;
  right: 0;
}

.line-title {
  width: 580px;
  height: 1px;
  background-color: #ccc;
  position: absolute;
  top: 50px
}

.line {
  width: 100%;
  height: 1px;
  background-color: #ccc;
}

.content-container {
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: 0 20px;

  :deep(.is-disabled .el-textarea__inner),
  :deep(.is-disabled .el-input__wrapper) {
    background-color: white;
    resize: none;
    /*禁止文本域拖拽事件*/
  }

  .q-type {
    display: inline-block;
    /*font-weight: bold;*/
    padding-left: 10px;
    white-space: nowrap;
  }

  .tip {
    margin-top: 10px;
  }

  .input {
    width: 100%;
    margin-bottom: 15px;
    margin-top: 10px;
  }

  .related-content-container {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: #d6e9f6;
    word-break: break-all;
    font-size: 14px;
    font-weight: 400;
  }

  .type-container {
    /*margin-top: 10px;*/
    display: flex;
    width: 100%;
    justify-content: space-between;
    white-space: nowrap;
    align-items: center;

    .type-tip {
      font-size: 14px;
      font-weight: 600;
    }
  }

  .button-list {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 15px;
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
</style>
<style>
#p_inline>p {
  display: inline;
}

.el-drawer__header {
  position: relative;
  padding-bottom: 10px;
}
</style>
