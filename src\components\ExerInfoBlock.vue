<script setup lang="ts">
import { findKeyByValue } from '@/utils/func';
import { getAllExerQuestionApi } from '@/apis/path/exercise';
import { inject, onMounted, onUnmounted, ref, toRaw, triggerRef, watch, nextTick } from 'vue';
import { ExerciseType, ExerciseTypeDict } from '@/utils/constant';
import { handleExercise, transferList2Exercise } from '@/utils/lineWordFunction';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { userInfoStore } from '@/stores/userInfo';
import { ElMessage } from 'element-plus';
import { convertMathTagsToMDLatex } from '@/utils/latexUtils';
import { Exercise } from '@/types/exercise';
import questionIcon from '@/assets/svgs/question.svg';
import { useRenderManager } from '@/composables/useRenderManager';
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import MulQuestionList from '@/views/prjdetail/components/MulQuestionList.vue';
import { getQuestionList } from '@/utils/lineWord2V2';
import { throttle } from 'lodash-es';

const content = inject('lineWordContent');

// 错误提示函数（从 lineWord2V2.ts 移植）
const throwErr = throttle((msg: string) => {
  ElMessage.error(msg);
}, 5000);

const questionList = ref<
  Array<{
    questionId: string;
    associatedWords: string;
  }>
>([]);
const answerList = ref<any[]>([]);

const exercise = ref<Exercise>({
  id: '',
  type: 0,
  stem: '',
  content: [],
  answer: '',
  explanation: ''
});

const props = defineProps<{
  exercise?: Exercise;
}>();

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  currentSelectedText,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon({
  onIconClick: (selectedText: string) => {
    // 使用 emitter 派发事件，与项目中的事件监听保持一致
    emitter.emit(Event.SHOW_QUESTION_DRAWER, {
      mode: 0,
      item: {
        content: selectedText,
        exerciseId: exercise.value
      }
    });
  }
});

// 引用 MulQuestionList 组件
const mulQuesionListRef = ref();

// 自定义处理点击问题的函数，覆盖默认行为
const handleClickWord = async (el: HTMLElement) => {
  // 获取问题列表
  const id = el.getAttribute('data-qid');

  if (id) {
    const questionList = await getQuestionList(id);

    if (questionList && questionList.length > 0) {
      // 显示浮窗，无论有几个问题
      if (mulQuesionListRef.value && mulQuesionListRef.value.showCard) {
        // 直接调用组件的方法
        mulQuesionListRef.value.showCard({
          questionList: questionList,
          element: el
        });
      }
    }
  }
};

// 使用Render管理器
const renderContent = ref();
const { destroyRenderInstance, initializeRender, addQuestion, removeQuestion } =
  useRenderManager({
    containerSelector: '#underline',
    getContentData: () => handleExercise(exercise.value),
    questionList,
    onSelect: (data: any) => {
      console.log('data', data);
      const currentSelection = data.selection || window.getSelection();
      const range = currentSelection.getRangeAt(0);
      const lineWordHtmlContents = document.getElementsByClassName('lineWordContent');

      let flag = false;
      for (let i = 0; i < lineWordHtmlContents.length; i++) {
        if (lineWordHtmlContents[i]?.contains(range.commonAncestorContainer)) {
          flag = true;
          break;
        }
      }

      if (!flag) {
        throwErr('请在内容区域内划词');
        return;
      }
      if (data && data.content) {
        showQuestionIcon(data);
      } else {
        console.log('❌ 选中文本为空或无效');
      }
    },

    onFinish: (arg: any) => {
      const content = arg.content;
      renderContent.value = content;
      // console.log("renderContent",renderContent.value);
    },

    onClick: (data: any) => {
      const questionElement = data.target;
      // 调用自定义的处理函数
      if (questionElement && questionElement instanceof HTMLElement) {
        handleClickWord(questionElement);
      }
    },

    enableDebugLog: true
  });

/**
 * 设置习题详情
 * @param exer 习题对象
 * @description
 * 1. 更新当前习题对象
 * 2. 如果是选择题类型(单选或多选):
 *    - 解析答案字符串为数组
 *    - 从习题选项中筛选出正确答案
 * 3. 获取并处理该习题相关的问题列表
 */
const setExerDetail = async (exer: any) => {
  // 更新当前习题对象
  exercise.value = exer;
  console.log('exercise.value', exercise.value);

  // 处理选择题答案
  if (exer.type === ExerciseType.multi || exer.type === ExerciseType.single) {
    // 解析答案字符串为数组
    const tempList = JSON.parse(exer.answer);
    // 筛选出正确答案选项
    answerList.value = exer.content
      .map((item) => {
        const isAnswer = tempList.some((tempItem) => tempItem === item.optionId);
        return isAnswer ? item : null;
      })
      .filter((item) => item !== null);
  }

  // 获取并处理习题相关的问题列表 - 等待异步操作完成
  await handleQuestionList(exer.exerciseId);
};
// 判断是否选择题
const isSelectionExer = () => {
  return exercise.value.type === ExerciseType.single || exercise.value.type === ExerciseType.multi;
};
// 获取全部问题
const handleQuestionList = async (exerciseId: string) => {
  const res = await getAllExerQuestionApi(exerciseId);
  if (!res.data) {
    console.warn('No question data received');
    return;
  }

  questionList.value = res.data;
  console.log('questionList.value', questionList.value);
};

// 添加问题函数
const addQuestionFn = (data: any) => {
  if (data.questionId && data.associatedWords) {
    // 使用 useRenderManager 的 addQuestion 方法
    addQuestion(data.associatedWords, Number(data.questionId));

    // 更新questionList
    questionList.value.push({
      questionId: data.questionId,
      associatedWords: data.associatedWords
    });
  }
};

// 删除问题函数
const removeQuestionFn = async (questionId: string) => {
  // 找到要删除的问题
  const questionIndex = questionList.value.findIndex((item) => item.questionId === questionId);
  if (questionIndex !== -1) {
    const question = questionList.value[questionIndex];

    // 使用 useRenderManager 的 removeQuestion 方法
    removeQuestion(question.associatedWords, Number(questionId));

    // 更新questionList
    questionList.value.splice(questionIndex, 1);
  }
};

// 模式切换相关代码已删除，统一处理交互逻辑

const emitterOnControler = () => {
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);
};
const emitterOffControler = () => {
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);
};
watch(
  () => props.exercise,
  async (newValue) => {
    if (newValue) {
      await setExerDetail(newValue);
      await initializeRender();
    }
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  // @ts-ignore
  // 将handleWord方法挂载到window对象上,用于全局调用
  // window.handleWord = handleWord;
  // 注册事件监听器,监听添加和删除问题事件
  emitterOnControler();
});

onUnmounted(() => {
  emitterOffControler();
  destroyRenderInstance(); // 清理 render 实例
});


watch(
  () => content,
  (newVal) => {
    // @ts-ignore
    if (newVal.value) {
      const query = {
        // @ts-ignore
        content: newVal.value,
        exerciseId: exercise.value
      };
      // @ts-ignore
      emitter.emit(Event.SHOW_QUESTION_DRAWER, { mode: 0, item: query });
    }
  },
  { deep: true, immediate: true }
);

// questionList 的变化由 useRenderManager 自动处理，无需手动监听

// 导出组件方法
defineExpose({
  addQuestionFn
});
</script>
<template>
  <div class="wrapper">
    <div class="header">
      <span class="title">{{ findKeyByValue(exercise.type, ExerciseTypeDict) }}</span>
    </div>
    <div class="main-container">
      <div id="underline">
        <div class="content-title">习题内容</div>
        <div class="content">
          <span class="lineWordContent ck-content renderItem" v-html="exercise.stem"></span>
          <div v-if="isSelectionExer()" style="margin: 0 20px" class="options">
            <div v-for="item in exercise.content" class="option">
              <span>{{ item.optionId }}.</span>
              <span
                class="lineWordContent ck-content renderItem"
                v-html="item.text"
                style="margin-left: 10px"
              ></span>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <div class="content-title">答案</div>
        <div class="content">
          <div v-if="isSelectionExer()" style="margin: 0 20px" class="options">
            <span v-for="item in answerList" class="option renderItem">
              <span>{{ item.optionId }}</span>
            </span>
          </div>
          <div v-if="exercise.type === ExerciseType.blank">
            <span class="lineWordContent ck-content" v-html="exercise.answer"></span>
          </div>
          <div v-if="exercise.type === ExerciseType.judge">
            {{ exercise.answer === '1' ? '正确' : '错误' }}
          </div>
        </div>
        <div class="line"></div>
        <div class="content-title">解析说明</div>

        <div class="content">
          <span class="renderItem lineWordContent ck-content" v-html="exercise.explanation"></span>
        </div>
      </div>
      <!-- 问号图标 -->
      <div
        v-if="questionIconVisible"
        ref="questionIconElement"
        class="question-icon"
        :style="{
          position: 'fixed',
          left: questionIconPosition.x + 'px',
          top: questionIconPosition.y + 'px',
          zIndex: 10000
        }"
        @click="handleQuestionIconClick"
      >
        <!-- 悬浮提示 -->
        <div class="question-tooltip">提问</div>
        <!-- 问号图标 -->
        <div class="question-icon-circle">
          <img :src="questionIcon" alt="" />
        </div>
      </div>
    </div>
    <!-- 问题列表浮窗 -->
    <MulQuestionList ref="mulQuesionListRef" id="mul-list"></MulQuestionList>
  </div>
</template>
<style scoped>
.wrapper {
  display: flex;
  width: 100%;
  flex-direction: column;
  background-color: white;
  height: 100%;
  /* padding: 10px; */
  .header {
    margin-left: 20px;
    display: flex;
    justify-content: flex-start;
    border-bottom: 1px solid var(--color-boxborder);
    .title {
      margin-left: 60px;
      margin-bottom: 15px;
      color: var(--color-primary);
      font-size: 16px;
      font-weight: 700;
    }
  }
  .main-container {
    display: flex;
    flex-direction: column;
    background-color: white;
    width: 100%;
    height: calc(100% - 60px); /* 减去header的高度 */
    padding: 0 80px;
    font-size: 14px;
    overflow-y: auto;
    overflow-x: hidden;
    /* :deep(pre) {
      display: flex;
      background-color: #f5f5f5;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 16px;
      font-size: 14px;
      font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier,
        monospace;
      line-height: 1.5;
      overflow-x: auto;
      color: #333;
    } */
    .content-title {
      margin: 20px 0;
      font-weight: 600;
    }
    .content {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin: 10px 0;
      .options {
        .option {
          margin: 20px 0;
        }
        &:deep(p) {
          display: inline;
        }
      }
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
