@import 'base.css';
@import 'reset.css';
#app {
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  overflow: hidden;
}
#app ::selection {
  background: #1973cb;
  color: #ffffff;
}
.titlefont {
  font-family: var(--title-family);
  font-weight: 600;
  font-style: normal;
}
.textfont {
  font-family: var(--text-family);
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
}
.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-center {
  display: flex;
  align-items: center;
  position: relative;
  /* 为绝对定位提供参考 */
}
.flex-start {
  display: flex;
  align-items: flex-start;
}
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, #9fe597, #cce581);
  max-width: 500px;
  text-wrap: wrap;
  color: black;
  z-index: 999 !important;
}
.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}
/* 省略号文本样式 */
.ellipsis-text {
  display: flex;
  max-width: calc(100% - 10px);
  /* 为引号预留更多空间 */
  color: black;
  /* 确保文字为黑色 */
  position: relative;
  /* 为渐变遮罩定位 */
  align-items: center;
  /* 垂直居中 */
  overflow: hidden;
  height: 100%;
}
/* 省略号文本样式 */
.ellipsis-text-inline {
  display: inline-flex;
  /* 使用inline-flex以支持垂直居中 */
  max-width: calc(100% - 10px);
  /* 为引号预留更多空间 */
  color: black;
  /* 确保文字为黑色 */
  position: relative;
  /* 为渐变遮罩定位 */
  align-items: center;
  /* 垂直居中 */
  overflow: hidden;
  height: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ellipsis-text::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  /* 渐变区域宽度 */
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), white);
  /* 从透明到白色的渐变 */
  pointer-events: none;
  /* 确保不影响鼠标事件 */
}
.ellipsis-text p,
.ellipsis-text-inline p {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
  /* 垂直居中 */
}
/* 问号图标样式 */
.question-icon {
  cursor: pointer;
  position: relative;
  animation: questionIconFadeIn 0.2s ease-in-out;
}
.question-icon-circle {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.question-icon-circle img {
  width: 16px;
  height: 16px;
}
.question-icon:hover .question-icon-circle {
  background: #f2f2f2;
}
/* 悬浮提示样式 */
.question-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  background: #666666;
  color: white;
  padding: 2px 5px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}
.question-icon:hover .question-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%);
}
/* 覆盖highlight.js库的.highlight样式 */
.highlight {
  color: inherit !important;
  background-color: transparent !important;
  cursor: inherit !important;
}
.highlightHover {
  text-decoration: none !important;
  font-size: 1.02em;
  background-color: #d6e9f6 !important;
  transform-origin: center;
  transition: background-color 0.2s ease-in-out;
  position: relative;
  z-index: 999;
}
.question-content .keyWords,
.keyword-container,
.question-info-row .keyWords,
.description .keyWords .keyword-container,
.keyWords {
  max-width: 100% !important;
  line-height: 25px !important;
  height: 25px !important;
}
.question-content .keyWords *:not(.equation *):not(.inline-equation *),
.keyword-container *:not(.equation *):not(.inline-equation *),
.question-info-row .keyWords *:not(.equation *):not(.inline-equation *),
.description .keyWords .keyword-container *:not(.equation *):not(.inline-equation *),
.keyWords *:not(.equation *):not(.inline-equation *) {
  display: inline-block !important;
}
.question-content .keyWords .equation,
.keyword-container .equation,
.question-info-row .keyWords .equation,
.description .keyWords .keyword-container .equation,
.keyWords .equation {
  display: inline-block !important;
  margin-top: -20px !important;
  cursor: pointer !important;
  transition: opacity 0.2s ease !important;
  font-family: var(--text-family);
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  font-size: 10px !important;
}
.question-content .keyWords .equation base,
.keyword-container .equation base,
.question-info-row .keyWords .equation base,
.description .keyWords .keyword-container .equation base,
.keyWords .equation base {
  height: 25px !important;
}
.question-content .keyWords img,
.keyword-container img,
.question-info-row .keyWords img,
.description .keyWords .keyword-container img,
.keyWords img {
  display: inline !important;
  height: 14px !important;
  cursor: pointer !important;
  transition: opacity 0.2s ease !important;
}
code:not(.vditor-reset code) {
  padding: 0.065em 0.4em;
  word-break: break-word;
  overflow-x: auto;
  background-color: #fff4f4 !important;
  border-radius: 2px;
  color: #c2185b !important;
}
code:not(.vditor-reset code) span {
  color: #c2185b !important;
}
.select_to_ask_time {
  width: 70px;
  font-weight: 600;
  color: #333333;
  font-size: 12px;
  margin-bottom: 5px;
  user-select: none;
}
